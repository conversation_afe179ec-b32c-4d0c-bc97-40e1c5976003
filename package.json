{"name": "@mallsurf-packages/auth", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "publishConfig": {"registry": "https://npm.pkg.github.com", "@mallsurf:registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/Mallsurf/mallsurf-auth.git"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "dependencies": {"@mallsurf-packages/core": "git+https://github.com/mallsurf-packages/mallsurf-core.git", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "rimraf": "^5.0.10", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "files": ["dist/**/*", "README.md"], "keywords": ["mallsurf", "auth", "authentication", "jwt"]}